<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika API 系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .links {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .link-button {
            display: inline-block;
            padding: 15px 30px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .link-button:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        .link-button.secondary {
            background: #28a745;
        }
        .link-button.secondary:hover {
            background: #1e7e34;
        }
        .api-list {
            text-align: left;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .api-list h3 {
            margin-top: 0;
            color: #333;
        }
        .api-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .api-list li {
            margin-bottom: 5px;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Mika API</h1>
        <div class="description">
            一个简单而强大的用户管理和卡密系统API
        </div>
        
        <div class="api-list">
            <h3>📋 API功能列表</h3>
            <ul>
                <li>用户注册与信息查询</li>
                <li>会员天数管理</li>
                <li>积分系统管理</li>
                <li>卡密生成与使用</li>
                <li>用户列表查看</li>
                <li>卡密状态查询</li>
            </ul>
        </div>
        
        <div class="links">
            <a href="docs.html" class="link-button">📖 查看API文档</a>
            <a href="test.php" class="link-button secondary">🧪 API测试工具</a>
        </div>
        
        <div style="margin-top: 30px; color: #999; font-size: 0.9em;">
            <p>🔐 所有API请求需要token验证</p>
            <p>📊 数据存储在JSON文件中</p>
            <p>🌐 支持跨域访问</p>
        </div>
    </div>
</body>
</html>
