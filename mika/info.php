<?php
// 查看用户信息API
require_once 'utils.php';

setCorsHeaders();
checkAuth();

$username = $_GET['username'] ?? '';

if (!validateUsername($username)) {
    sendResponse(400, 'Invalid username', []);
    exit();
}

// 读取用户数据
$users = readJsonFile(USERS_FILE);

// 查找用户
foreach ($users as $user) {
    if ($user['username'] === $username) {
        sendResponse(200, 'User info retrieved successfully', [
            'username' => $user['username'],
            'vip_days' => $user['vip_days'],
            'points' => $user['points'],
            'reg_time' => $user['reg_time']
        ]);
        exit();
    }
}

sendResponse(404, 'User not found', []);
?>
