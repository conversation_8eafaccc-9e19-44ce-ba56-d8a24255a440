<?php
// 积分充值/扣除API
require_once 'utils.php';

setCorsHeaders();
checkAuth();

$username = $_GET['username'] ?? '';
$points = $_GET['points'] ?? '';

if (!validateUsername($username)) {
    sendResponse(400, 'Invalid username', []);
    exit();
}

if (!is_numeric($points)) {
    sendResponse(400, 'Invalid points value', []);
    exit();
}

$points = (int)$points;

// 读取用户数据
$users = readJsonFile(USERS_FILE);

// 查找并更新用户
$userFound = false;
for ($i = 0; $i < count($users); $i++) {
    if ($users[$i]['username'] === $username) {
        $users[$i]['points'] += $points;
        
        // 确保积分不为负数
        if ($users[$i]['points'] < 0) {
            $users[$i]['points'] = 0;
        }
        
        $userFound = true;
        
        // 保存数据
        if (writeJsonFile(USERS_FILE, $users)) {
            sendResponse(200, 'Points updated successfully', [
                'username' => $users[$i]['username'],
                'vip_days' => $users[$i]['vip_days'],
                'points' => $users[$i]['points'],
                'reg_time' => $users[$i]['reg_time']
            ]);
        } else {
            sendResponse(500, 'Failed to update points', []);
        }
        break;
    }
}

if (!$userFound) {
    sendResponse(404, 'User not found', []);
}
?>
