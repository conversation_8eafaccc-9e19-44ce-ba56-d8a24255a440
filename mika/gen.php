<?php
// 生成卡密API
require_once 'utils.php';

setCorsHeaders();
checkAuth();

$type = $_GET['type'] ?? '';
$count = $_GET['count'] ?? '';
$value = $_GET['value'] ?? '';

if (!in_array($type, ['vip', 'points'])) {
    sendResponse(400, 'Invalid card type (vip/points)', []);
    exit();
}

if (!is_numeric($count) || $count <= 0 || $count > 100) {
    sendResponse(400, 'Invalid count (1-100)', []);
    exit();
}

if (!is_numeric($value) || $value <= 0) {
    sendResponse(400, 'Invalid value', []);
    exit();
}

$count = (int)$count;
$value = (int)$value;

// 读取现有卡密数据
$cards = readJsonFile(CARDS_FILE);

$generatedCards = [];
$cardKeys = [];

for ($i = 0; $i < $count; $i++) {
    $cardKey = generateCardKey($type, $value);
    
    // 确保卡密唯一
    while (isset($cards[$cardKey])) {
        $cardKey = generateCardKey($type, $value);
    }
    
    $cardData = [
        'key' => $cardKey,
        'type' => $type,
        'value' => $value,
        'gen_time' => getCurrentTime(),
        'use_time' => null,
        'use_user' => null,
        'status' => 'unused'
    ];
    
    $cards[$cardKey] = $cardData;
    $generatedCards[] = $cardData;
    $cardKeys[] = $cardKey;
}

// 保存卡密数据
if (writeJsonFile(CARDS_FILE, $cards)) {
    $responseKey = $type === 'vip' ? 'vipkami' : 'jifenkami';
    sendResponse(200, 'Cards generated successfully', [
        $responseKey => implode('-', $cardKeys)
    ]);
} else {
    sendResponse(500, 'Failed to generate cards', []);
}
?>
