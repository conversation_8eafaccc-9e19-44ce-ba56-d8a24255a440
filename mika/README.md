# Mika API 系统

一个简单而强大的用户管理和卡密系统API，使用PHP开发，数据存储在JSON文件中。

## 🚀 功能特性

- ✅ 用户注册与信息管理
- ✅ 会员天数充值/扣除
- ✅ 积分系统管理
- ✅ 卡密生成与使用
- ✅ 用户列表查看
- ✅ 卡密状态查询
- ✅ 跨域支持
- ✅ 简单的Token鉴权

## 📁 文件结构

```
mika/
├── index.php          # 系统首页
├── docs.html          # API文档
├── test.php           # API测试工具
├── utils.php          # 工具函数
├── reg.php            # 用户注册API
├── info.php           # 查看用户信息API
├── vip.php            # 会员天数管理API
├── points.php         # 积分管理API
├── list.php           # 用户列表API
├── gen.php            # 生成卡密API
├── use.php            # 使用卡密API
├── card.php           # 查看卡密API
├── data/              # 数据存储目录（自动创建）
│   ├── users.json     # 用户数据
│   └── cards.json     # 卡密数据
└── README.md          # 说明文档
```

## 🔧 安装与配置

1. 将mika文件夹上传到您的Web服务器
2. 确保PHP环境支持文件读写权限
3. 访问 `http://your-domain/mika/` 查看系统首页

## 🔐 鉴权说明

所有API请求都需要在URL参数中包含token：
```
?token=mika2024
```

## 📖 API列表

| API | 文件 | 功能 | 参数 |
|-----|------|------|------|
| 注册用户 | reg.php | 创建新用户 | username |
| 查看用户信息 | info.php | 获取用户详情 | username |
| 会员天数管理 | vip.php | 充值/扣除会员天数 | username, days |
| 积分管理 | points.php | 充值/扣除积分 | username, points |
| 用户列表 | list.php | 获取所有用户 | 无 |
| 生成卡密 | gen.php | 创建新卡密 | type, count, value |
| 使用卡密 | use.php | 消费卡密 | username, card |
| 查看卡密 | card.php | 查询卡密信息 | card |

## 🧪 测试

访问 `test.php` 使用内置的测试工具，或查看 `docs.html` 获取详细的API文档。

## 📊 数据格式

### 用户数据结构
```json
{
  "username": "用户名",
  "vip_days": 0,
  "points": 0,
  "reg_time": "2024-08-18 15:30:00"
}
```

### 卡密数据结构
```json
{
  "key": "卡密内容",
  "type": "vip|points",
  "value": 30,
  "gen_time": "生成时间",
  "use_time": "使用时间",
  "use_user": "使用用户",
  "status": "unused|used"
}
```

## 🛡️ 安全注意事项

1. 修改默认token值 `mika2024`
2. 限制API访问IP
3. 定期备份数据文件
4. 考虑使用更强的鉴权机制

## 📝 响应格式

所有API都返回统一的JSON格式：
```json
{
  "code": 200,
  "msg": "操作信息",
  "data": {}
}
```

## 🔄 状态码

- 200: 成功
- 400: 请求参数错误
- 401: 未授权（token错误）
- 404: 资源不存在
- 409: 冲突（如用户已存在）
- 500: 服务器内部错误

## 📞 技术支持

如有问题，请检查：
1. PHP版本兼容性
2. 文件读写权限
3. Token是否正确
4. 参数格式是否正确

---

**Mika API System** - 简单、高效、易用的用户管理API
