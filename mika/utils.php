<?php
// 工具函数文件

// 设置跨域和响应头
function setCorsHeaders() {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    header('Content-Type: application/json; charset=utf-8');
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// 简单的鉴权检查
function checkAuth() {
    $token = $_GET['token'] ?? '';
    $validToken = 'mika2024'; // 简单的token验证
    
    if ($token !== $validToken) {
        sendResponse(401, 'Unauthorized', []);
        exit();
    }
}

// 发送JSON响应
function sendResponse($code, $msg, $data = []) {
    http_response_code($code);
    echo json_encode([
        'code' => $code,
        'msg' => $msg,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
}

// 读取JSON文件
function readJsonFile($filename) {
    if (!file_exists($filename)) {
        return [];
    }
    $content = file_get_contents($filename);
    return json_decode($content, true) ?: [];
}

// 写入JSON文件
function writeJsonFile($filename, $data) {
    return file_put_contents($filename, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
}

// 生成随机卡密
function generateCardKey($type, $value) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $key = '';
    for ($i = 0; $i < 8; $i++) {
        $key .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $key;
}

// 获取当前时间
function getCurrentTime() {
    return date('Y-m-d H:i:s');
}

// 验证用户名
function validateUsername($username) {
    return !empty($username) && strlen($username) >= 2 && strlen($username) <= 20;
}

// 数据文件路径
define('USERS_FILE', __DIR__ . '/data/users.json');
define('CARDS_FILE', __DIR__ . '/data/cards.json');

// 确保数据目录存在
if (!is_dir(__DIR__ . '/data')) {
    mkdir(__DIR__ . '/data', 0755, true);
}
?>
