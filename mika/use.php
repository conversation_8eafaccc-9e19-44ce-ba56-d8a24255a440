<?php
// 使用卡密API
require_once 'utils.php';

setCorsHeaders();
checkAuth();

$username = $_GET['username'] ?? '';
$cardKey = $_GET['card'] ?? '';

if (!validateUsername($username)) {
    sendResponse(400, 'Invalid username', []);
    exit();
}

if (empty($cardKey)) {
    sendResponse(400, 'Card key required', []);
    exit();
}

// 读取用户数据和卡密数据
$users = readJsonFile(USERS_FILE);
$cards = readJsonFile(CARDS_FILE);

// 检查卡密是否存在
if (!isset($cards[$cardKey])) {
    sendResponse(404, 'Card not found', []);
    exit();
}

$card = $cards[$cardKey];

// 检查卡密是否已使用
if ($card['status'] === 'used') {
    sendResponse(409, 'Card already used', []);
    exit();
}

// 查找用户
$userFound = false;
$originalVipDays = 0;
$originalPoints = 0;
$newVipDays = 0;
$newPoints = 0;

for ($i = 0; $i < count($users); $i++) {
    if ($users[$i]['username'] === $username) {
        $originalVipDays = $users[$i]['vip_days'];
        $originalPoints = $users[$i]['points'];
        
        // 根据卡密类型增加相应数值
        if ($card['type'] === 'vip') {
            $users[$i]['vip_days'] += $card['value'];
        } else {
            $users[$i]['points'] += $card['value'];
        }
        
        $newVipDays = $users[$i]['vip_days'];
        $newPoints = $users[$i]['points'];
        
        $userFound = true;
        break;
    }
}

if (!$userFound) {
    sendResponse(404, 'User not found', []);
    exit();
}

// 标记卡密为已使用
$cards[$cardKey]['status'] = 'used';
$cards[$cardKey]['use_time'] = getCurrentTime();
$cards[$cardKey]['use_user'] = $username;

// 保存数据
if (writeJsonFile(USERS_FILE, $users) && writeJsonFile(CARDS_FILE, $cards)) {
    sendResponse(200, 'Card used successfully', [
        'username' => $username,
        'old_vip_days' => $originalVipDays,
        'new_vip_days' => $newVipDays,
        'old_points' => $originalPoints,
        'new_points' => $newPoints
    ]);
} else {
    sendResponse(500, 'Failed to use card', []);
}
?>
