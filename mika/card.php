<?php
// 查看卡密API
require_once 'utils.php';

setCorsHeaders();
checkAuth();

$cardKey = $_GET['card'] ?? '';

if (empty($cardKey)) {
    sendResponse(400, 'Card key required', []);
    exit();
}

// 读取卡密数据
$cards = readJsonFile(CARDS_FILE);

// 检查卡密是否存在
if (!isset($cards[$cardKey])) {
    sendResponse(404, 'Card not found', []);
    exit();
}

$card = $cards[$cardKey];

sendResponse(200, 'Card info retrieved successfully', [
    'key' => $card['key'],
    'type' => $card['type'],
    'value' => $card['value'],
    'gen_time' => $card['gen_time'],
    'use_time' => $card['use_time'],
    'use_user' => $card['use_user'],
    'status' => $card['status']
]);
?>
