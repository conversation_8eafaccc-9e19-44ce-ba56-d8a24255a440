<?php
// API测试脚本
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mika API 测试工具</h1>
        
        <!-- 注册用户 -->
        <div class="test-section">
            <h3>1. 注册用户</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="reg_username" placeholder="输入用户名">
            </div>
            <button onclick="testRegister()">注册用户</button>
            <div id="reg_result" class="result"></div>
        </div>

        <!-- 查看用户信息 -->
        <div class="test-section">
            <h3>2. 查看用户信息</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="info_username" placeholder="输入用户名">
            </div>
            <button onclick="testUserInfo()">查看信息</button>
            <div id="info_result" class="result"></div>
        </div>

        <!-- 会员天数操作 -->
        <div class="test-section">
            <h3>3. 会员天数充值/扣除</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="vip_username" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>天数:</label>
                <input type="number" id="vip_days" placeholder="正数充值，负数扣除">
            </div>
            <button onclick="testVipDays()">更新会员天数</button>
            <div id="vip_result" class="result"></div>
        </div>

        <!-- 积分操作 -->
        <div class="test-section">
            <h3>4. 积分充值/扣除</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="points_username" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>积分:</label>
                <input type="number" id="points_value" placeholder="正数充值，负数扣除">
            </div>
            <button onclick="testPoints()">更新积分</button>
            <div id="points_result" class="result"></div>
        </div>

        <!-- 查看用户列表 -->
        <div class="test-section">
            <h3>5. 查看所有用户</h3>
            <button onclick="testUserList()">获取用户列表</button>
            <div id="list_result" class="result"></div>
        </div>

        <!-- 生成卡密 -->
        <div class="test-section">
            <h3>6. 生成卡密</h3>
            <div class="form-group">
                <label>卡密类型:</label>
                <select id="gen_type">
                    <option value="vip">VIP会员</option>
                    <option value="points">积分</option>
                </select>
            </div>
            <div class="form-group">
                <label>生成数量:</label>
                <input type="number" id="gen_count" value="1" min="1" max="100">
            </div>
            <div class="form-group">
                <label>卡密数值:</label>
                <input type="number" id="gen_value" placeholder="会员天数或积分数量">
            </div>
            <button onclick="testGenerate()">生成卡密</button>
            <div id="gen_result" class="result"></div>
        </div>

        <!-- 使用卡密 -->
        <div class="test-section">
            <h3>7. 使用卡密</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="use_username" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>卡密:</label>
                <input type="text" id="use_card" placeholder="输入卡密">
            </div>
            <button onclick="testUseCard()">使用卡密</button>
            <div id="use_result" class="result"></div>
        </div>

        <!-- 查看卡密 -->
        <div class="test-section">
            <h3>8. 查看卡密</h3>
            <div class="form-group">
                <label>卡密:</label>
                <input type="text" id="card_key" placeholder="输入卡密">
            </div>
            <button onclick="testCardInfo()">查看卡密</button>
            <div id="card_result" class="result"></div>
        </div>
    </div>

    <script>
        const token = 'mika2024';
        const baseUrl = './mika/';

        async function apiCall(endpoint, params = {}) {
            const url = new URL(endpoint, baseUrl);
            url.searchParams.append('token', token);
            
            for (const [key, value] of Object.entries(params)) {
                if (value !== '') {
                    url.searchParams.append(key, value);
                }
            }

            try {
                const response = await fetch(url);
                const data = await response.json();
                return JSON.stringify(data, null, 2);
            } catch (error) {
                return `Error: ${error.message}`;
            }
        }

        async function testRegister() {
            const username = document.getElementById('reg_username').value;
            const result = await apiCall('reg.php', { username });
            document.getElementById('reg_result').textContent = result;
        }

        async function testUserInfo() {
            const username = document.getElementById('info_username').value;
            const result = await apiCall('info.php', { username });
            document.getElementById('info_result').textContent = result;
        }

        async function testVipDays() {
            const username = document.getElementById('vip_username').value;
            const days = document.getElementById('vip_days').value;
            const result = await apiCall('vip.php', { username, days });
            document.getElementById('vip_result').textContent = result;
        }

        async function testPoints() {
            const username = document.getElementById('points_username').value;
            const points = document.getElementById('points_value').value;
            const result = await apiCall('points.php', { username, points });
            document.getElementById('points_result').textContent = result;
        }

        async function testUserList() {
            const result = await apiCall('list.php');
            document.getElementById('list_result').textContent = result;
        }

        async function testGenerate() {
            const type = document.getElementById('gen_type').value;
            const count = document.getElementById('gen_count').value;
            const value = document.getElementById('gen_value').value;
            const result = await apiCall('gen.php', { type, count, value });
            document.getElementById('gen_result').textContent = result;
        }

        async function testUseCard() {
            const username = document.getElementById('use_username').value;
            const card = document.getElementById('use_card').value;
            const result = await apiCall('use.php', { username, card });
            document.getElementById('use_result').textContent = result;
        }

        async function testCardInfo() {
            const card = document.getElementById('card_key').value;
            const result = await apiCall('card.php', { card });
            document.getElementById('card_result').textContent = result;
        }
    </script>
</body>
</html>
