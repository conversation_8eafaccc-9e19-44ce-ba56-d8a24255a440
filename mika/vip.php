<?php
// 会员天数充值/扣除API
require_once 'utils.php';

setCorsHeaders();
checkAuth();

$username = $_GET['username'] ?? '';
$days = $_GET['days'] ?? '';

if (!validateUsername($username)) {
    sendResponse(400, 'Invalid username', []);
    exit();
}

if (!is_numeric($days)) {
    sendResponse(400, 'Invalid days value', []);
    exit();
}

$days = (int)$days;

// 读取用户数据
$users = readJsonFile(USERS_FILE);

// 查找并更新用户
$userFound = false;
for ($i = 0; $i < count($users); $i++) {
    if ($users[$i]['username'] === $username) {
        $users[$i]['vip_days'] += $days;
        
        // 确保会员天数不为负数
        if ($users[$i]['vip_days'] < 0) {
            $users[$i]['vip_days'] = 0;
        }
        
        $userFound = true;
        
        // 保存数据
        if (writeJsonFile(USERS_FILE, $users)) {
            sendResponse(200, 'VIP days updated successfully', [
                'username' => $users[$i]['username'],
                'vip_days' => $users[$i]['vip_days'],
                'points' => $users[$i]['points'],
                'reg_time' => $users[$i]['reg_time']
            ]);
        } else {
            sendResponse(500, 'Failed to update VIP days', []);
        }
        break;
    }
}

if (!$userFound) {
    sendResponse(404, 'User not found', []);
}
?>
