<?php
// 用户注册API
require_once 'utils.php';

setCorsHeaders();
checkAuth();

$username = $_GET['username'] ?? '';

if (!validateUsername($username)) {
    sendResponse(400, 'Invalid username', []);
    exit();
}

// 读取现有用户数据
$users = readJsonFile(USERS_FILE);

// 检查用户是否已存在
foreach ($users as $user) {
    if ($user['username'] === $username) {
        sendResponse(409, 'User already exists', []);
        exit();
    }
}

// 创建新用户
$newUser = [
    'username' => $username,
    'vip_days' => 0,
    'points' => 0,
    'reg_time' => getCurrentTime()
];

$users[] = $newUser;

// 保存用户数据
if (writeJsonFile(USERS_FILE, $users)) {
    sendResponse(200, 'User registered successfully', [
        'username' => $username,
        'vip_days' => 0,
        'points' => 0,
        'reg_time' => $newUser['reg_time']
    ]);
} else {
    sendResponse(500, 'Failed to register user', []);
}
?>
