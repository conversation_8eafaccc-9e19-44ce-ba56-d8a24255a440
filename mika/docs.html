<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika API 文档</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #007bff;
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-top: 30px;
        }
        .api-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .method {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        .endpoint {
            background: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .params, .response {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .params h4, .response h4 {
            margin-top: 0;
            color: #495057;
        }
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 13px;
        }
        .param-item {
            margin-bottom: 8px;
        }
        .param-name {
            font-weight: bold;
            color: #007bff;
        }
        .param-type {
            color: #28a745;
            font-style: italic;
        }
        .required {
            color: #dc3545;
            font-size: 12px;
        }
        .auth-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mika API 文档</h1>
        
        <div class="auth-note">
            <strong>鉴权说明：</strong> 所有API请求都需要在URL参数中包含 <code>token=mika2024</code>
        </div>

        <!-- 1. 注册用户 -->
        <div class="api-section">
            <h2>1. 注册用户</h2>
            <div class="method">GET</div>
            <div class="endpoint">GET /mika/reg.php?token=mika2024&username={username}</div>
            
            <div class="params">
                <h4>请求参数</h4>
                <div class="param-item">
                    <span class="param-name">token</span> 
                    <span class="param-type">(string)</span> 
                    <span class="required">必需</span> - 鉴权令牌，固定值：mika2024
                </div>
                <div class="param-item">
                    <span class="param-name">username</span> 
                    <span class="param-type">(string)</span> 
                    <span class="required">必需</span> - 用户名，2-20个字符
                </div>
            </div>

            <div class="response">
                <h4>请求示例</h4>
                <pre>GET /mika/reg.php?token=mika2024&username=testuser</pre>
                
                <h4>响应示例</h4>
                <pre>{
  "code": 200,
  "msg": "User registered successfully",
  "data": {
    "username": "testuser",
    "vip_days": 0,
    "points": 0,
    "reg_time": "2024-08-18 15:30:00"
  }
}</pre>
                
                <h4>响应参数说明</h4>
                <div class="param-item"><span class="param-name">code</span> - 状态码</div>
                <div class="param-item"><span class="param-name">msg</span> - 状态信息</div>
                <div class="param-item"><span class="param-name">data.username</span> - 用户名</div>
                <div class="param-item"><span class="param-name">data.vip_days</span> - 用户剩余会员天数</div>
                <div class="param-item"><span class="param-name">data.points</span> - 用户剩余积分</div>
                <div class="param-item"><span class="param-name">data.reg_time</span> - 用户注册时间</div>
            </div>
        </div>

        <!-- 2. 查看用户信息 -->
        <div class="api-section">
            <h2>2. 查看用户信息</h2>
            <div class="method">GET</div>
            <div class="endpoint">GET /mika/info.php?token=mika2024&username={username}</div>
            
            <div class="params">
                <h4>请求参数</h4>
                <div class="param-item">
                    <span class="param-name">token</span> 
                    <span class="param-type">(string)</span> 
                    <span class="required">必需</span> - 鉴权令牌
                </div>
                <div class="param-item">
                    <span class="param-name">username</span> 
                    <span class="param-type">(string)</span> 
                    <span class="required">必需</span> - 用户名
                </div>
            </div>

            <div class="response">
                <h4>请求示例</h4>
                <pre>GET /mika/info.php?token=mika2024&username=testuser</pre>
                
                <h4>响应示例</h4>
                <pre>{
  "code": 200,
  "msg": "User info retrieved successfully",
  "data": {
    "username": "testuser",
    "vip_days": 30,
    "points": 100,
    "reg_time": "2024-08-18 15:30:00"
  }
}</pre>
            </div>
        </div>

        <!-- 3. 会员天数充值/扣除 -->
        <div class="api-section">
            <h2>3. 会员天数充值/扣除</h2>
            <div class="method">GET</div>
            <div class="endpoint">GET /mika/vip.php?token=mika2024&username={username}&days={days}</div>
            
            <div class="params">
                <h4>请求参数</h4>
                <div class="param-item">
                    <span class="param-name">token</span> 
                    <span class="param-type">(string)</span> 
                    <span class="required">必需</span> - 鉴权令牌
                </div>
                <div class="param-item">
                    <span class="param-name">username</span> 
                    <span class="param-type">(string)</span> 
                    <span class="required">必需</span> - 用户名
                </div>
                <div class="param-item">
                    <span class="param-name">days</span> 
                    <span class="param-type">(integer)</span> 
                    <span class="required">必需</span> - 天数（正数充值，负数扣除）
                </div>
            </div>

            <div class="response">
                <h4>请求示例</h4>
                <pre>GET /mika/vip.php?token=mika2024&username=testuser&days=30</pre>
                
                <h4>响应示例</h4>
                <pre>{
  "code": 200,
  "msg": "VIP days updated successfully",
  "data": {
    "username": "testuser",
    "vip_days": 60,
    "points": 100,
    "reg_time": "2024-08-18 15:30:00"
  }
}</pre>
            </div>
        </div>

        <!-- 4. 积分充值/扣除 -->
        <div class="api-section">
            <h2>4. 积分充值/扣除</h2>
            <div class="method">GET</div>
            <div class="endpoint">GET /mika/points.php?token=mika2024&username={username}&points={points}</div>

            <div class="params">
                <h4>请求参数</h4>
                <div class="param-item">
                    <span class="param-name">token</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 鉴权令牌
                </div>
                <div class="param-item">
                    <span class="param-name">username</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 用户名
                </div>
                <div class="param-item">
                    <span class="param-name">points</span>
                    <span class="param-type">(integer)</span>
                    <span class="required">必需</span> - 积分数量（正数充值，负数扣除）
                </div>
            </div>

            <div class="response">
                <h4>请求示例</h4>
                <pre>GET /mika/points.php?token=mika2024&username=testuser&points=50</pre>

                <h4>响应示例</h4>
                <pre>{
  "code": 200,
  "msg": "Points updated successfully",
  "data": {
    "username": "testuser",
    "vip_days": 60,
    "points": 150,
    "reg_time": "2024-08-18 15:30:00"
  }
}</pre>
            </div>
        </div>

        <!-- 5. 查看所有用户列表 -->
        <div class="api-section">
            <h2>5. 查看所有用户列表</h2>
            <div class="method">GET</div>
            <div class="endpoint">GET /mika/list.php?token=mika2024</div>

            <div class="params">
                <h4>请求参数</h4>
                <div class="param-item">
                    <span class="param-name">token</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 鉴权令牌
                </div>
            </div>

            <div class="response">
                <h4>请求示例</h4>
                <pre>GET /mika/list.php?token=mika2024</pre>

                <h4>响应示例</h4>
                <pre>{
  "code": 200,
  "msg": "User list retrieved successfully",
  "data": [
    {
      "username": "testuser",
      "vip_days": 60,
      "points": 150,
      "reg_time": "2024-08-18 15:30:00"
    },
    {
      "username": "user2",
      "vip_days": 0,
      "points": 0,
      "reg_time": "2024-08-18 16:00:00"
    }
  ]
}</pre>
            </div>
        </div>

        <!-- 6. 生成卡密 -->
        <div class="api-section">
            <h2>6. 生成卡密</h2>
            <div class="method">GET</div>
            <div class="endpoint">GET /mika/gen.php?token=mika2024&type={type}&count={count}&value={value}</div>

            <div class="params">
                <h4>请求参数</h4>
                <div class="param-item">
                    <span class="param-name">token</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 鉴权令牌
                </div>
                <div class="param-item">
                    <span class="param-name">type</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 卡密类型：vip 或 points
                </div>
                <div class="param-item">
                    <span class="param-name">count</span>
                    <span class="param-type">(integer)</span>
                    <span class="required">必需</span> - 生成数量（1-100）
                </div>
                <div class="param-item">
                    <span class="param-name">value</span>
                    <span class="param-type">(integer)</span>
                    <span class="required">必需</span> - 卡密数值（会员天数或积分数量）
                </div>
            </div>

            <div class="response">
                <h4>请求示例</h4>
                <pre>GET /mika/gen.php?token=mika2024&type=vip&count=3&value=30</pre>

                <h4>响应示例</h4>
                <pre>{
  "code": 200,
  "msg": "Cards generated successfully",
  "data": {
    "vipkami": "ABC12345-DEF67890-GHI09876"
  }
}</pre>

                <h4>响应参数说明</h4>
                <div class="param-item"><span class="param-name">vipkami</span> - VIP卡密列表（用-分割）</div>
                <div class="param-item"><span class="param-name">jifenkami</span> - 积分卡密列表（用-分割）</div>
            </div>
        </div>

        <!-- 7. 使用卡密 -->
        <div class="api-section">
            <h2>7. 使用卡密</h2>
            <div class="method">GET</div>
            <div class="endpoint">GET /mika/use.php?token=mika2024&username={username}&card={card}</div>

            <div class="params">
                <h4>请求参数</h4>
                <div class="param-item">
                    <span class="param-name">token</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 鉴权令牌
                </div>
                <div class="param-item">
                    <span class="param-name">username</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 用户名
                </div>
                <div class="param-item">
                    <span class="param-name">card</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 卡密内容
                </div>
            </div>

            <div class="response">
                <h4>请求示例</h4>
                <pre>GET /mika/use.php?token=mika2024&username=testuser&card=ABC12345</pre>

                <h4>响应示例</h4>
                <pre>{
  "code": 200,
  "msg": "Card used successfully",
  "data": {
    "username": "testuser",
    "old_vip_days": 60,
    "new_vip_days": 90,
    "old_points": 150,
    "new_points": 150
  }
}</pre>

                <h4>响应参数说明</h4>
                <div class="param-item"><span class="param-name">username</span> - 用户名</div>
                <div class="param-item"><span class="param-name">old_vip_days</span> - 原本会员天数</div>
                <div class="param-item"><span class="param-name">new_vip_days</span> - 现在会员天数</div>
                <div class="param-item"><span class="param-name">old_points</span> - 原本剩余积分</div>
                <div class="param-item"><span class="param-name">new_points</span> - 现在剩余积分</div>
            </div>
        </div>

        <!-- 8. 查看卡密 -->
        <div class="api-section">
            <h2>8. 查看卡密</h2>
            <div class="method">GET</div>
            <div class="endpoint">GET /mika/card.php?token=mika2024&card={card}</div>

            <div class="params">
                <h4>请求参数</h4>
                <div class="param-item">
                    <span class="param-name">token</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 鉴权令牌
                </div>
                <div class="param-item">
                    <span class="param-name">card</span>
                    <span class="param-type">(string)</span>
                    <span class="required">必需</span> - 卡密内容
                </div>
            </div>

            <div class="response">
                <h4>请求示例</h4>
                <pre>GET /mika/card.php?token=mika2024&card=ABC12345</pre>

                <h4>响应示例</h4>
                <pre>{
  "code": 200,
  "msg": "Card info retrieved successfully",
  "data": {
    "key": "ABC12345",
    "type": "vip",
    "value": 30,
    "gen_time": "2024-08-18 15:45:00",
    "use_time": "2024-08-18 16:00:00",
    "use_user": "testuser",
    "status": "used"
  }
}</pre>

                <h4>响应参数说明</h4>
                <div class="param-item"><span class="param-name">key</span> - 卡密内容</div>
                <div class="param-item"><span class="param-name">type</span> - 卡密类型（vip/points）</div>
                <div class="param-item"><span class="param-name">value</span> - 卡密数值</div>
                <div class="param-item"><span class="param-name">gen_time</span> - 生成时间</div>
                <div class="param-item"><span class="param-name">use_time</span> - 使用时间</div>
                <div class="param-item"><span class="param-name">use_user</span> - 使用用户</div>
                <div class="param-item"><span class="param-name">status</span> - 卡密状态（unused/used）</div>
            </div>
        </div>

    </div>
</body>
</html>
